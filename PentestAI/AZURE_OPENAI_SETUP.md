# Azure OpenAI Setup Guide for PentestAI

This guide explains how to configure and use Azure OpenAI with the PentestAI system.

## Prerequisites

1. **Azure Subscription**: You need an active Azure subscription
2. **Azure OpenAI Resource**: Create an Azure OpenAI resource in the Azure portal
3. **Model Deployment**: Deploy a model (e.g., GPT-4o, GPT-4 Turbo) in your Azure OpenAI resource

## Step 1: Create Azure OpenAI Resource

1. Go to the [Azure Portal](https://portal.azure.com)
2. Search for "Azure OpenAI" and create a new resource
3. Choose your subscription, resource group, and region
4. Select a pricing tier
5. Wait for the deployment to complete

## Step 2: Deploy a Model

1. Navigate to your Azure OpenAI resource
2. Go to "Model deployments" or use Azure OpenAI Studio
3. Create a new deployment:
   - Choose a model (recommended: `gpt-4o` or `gpt-4-turbo`)
   - Give it a deployment name (e.g., `gpt-4o-deployment`)
   - Configure capacity settings

## Step 3: Get Your Configuration Details

From your Azure OpenAI resource, collect:

- **API Key**: Found in "Keys and Endpoint" section
- **Endpoint**: The endpoint URL (e.g., `https://your-resource.openai.azure.com/`)
- **Deployment Name**: The name you gave your model deployment
- **API Version**: Use `2024-02-15-preview` (recommended)

## Step 4: Configure PentestAI

1. **Copy the environment template**:
   ```bash
   cp .env.example .env
   ```

2. **Edit your `.env` file** with your Azure OpenAI details:
   ```bash
   # LLM Provider Configuration
   LLM_PROVIDER=azure_openai
   
   # Azure OpenAI Configuration
   AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
   AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
   AZURE_OPENAI_DEPLOYMENT=your_deployment_name_here
   AZURE_OPENAI_API_VERSION=2024-02-15-preview
   
   # Other configuration (keep existing values)
   MCP_SERVER_URL=http://localhost:8931/mcp
   MCP_SERVER_PORT=8931
   BROWSER_TYPE=chromium
   BROWSER_HEADLESS=true
   REPORTS_DIR=./reports
   ```

## Step 5: Test Your Configuration

Run the test script to verify everything is working:

```bash
python example_azure_openai.py
```

This will:
- Load your configuration
- Validate the settings
- Create an Azure OpenAI client
- Test basic chat functionality

## Step 6: Run PentestAI with Azure OpenAI

Now you can use PentestAI with Azure OpenAI:

```bash
# Run a SQL injection test
python main.py --url https://example.com --vuln SQLI

# Run an XSS test
python main.py --url https://example.com --vuln XSS
```

## Environment Variables Reference

| Variable | Description | Example |
|----------|-------------|---------|
| `LLM_PROVIDER` | Set to `azure_openai` | `azure_openai` |
| `AZURE_OPENAI_API_KEY` | Your Azure OpenAI API key | `abc123...` |
| `AZURE_OPENAI_ENDPOINT` | Your Azure OpenAI endpoint URL | `https://myresource.openai.azure.com/` |
| `AZURE_OPENAI_DEPLOYMENT` | Your model deployment name | `gpt-4o-deployment` |
| `AZURE_OPENAI_API_VERSION` | API version to use | `2024-02-15-preview` |

## Troubleshooting

### Common Issues

1. **Authentication Error**:
   - Verify your API key is correct
   - Check that your Azure OpenAI resource is active

2. **Deployment Not Found**:
   - Ensure the deployment name matches exactly
   - Verify the deployment is in "Succeeded" state

3. **Endpoint Issues**:
   - Make sure the endpoint URL includes the trailing slash
   - Verify the endpoint format: `https://your-resource.openai.azure.com/`

4. **API Version Issues**:
   - Use a supported API version (recommended: `2024-02-15-preview`)
   - Check Azure OpenAI documentation for latest versions

### Testing Individual Components

You can test individual components:

```python
# Test configuration loading
from shared.config import get_pentest_config, validate_config
config = get_pentest_config()
validate_config(config)

# Test LLM factory
from shared.llm_factory import LLMFactory
llm = LLMFactory.create_llm(config.llm)

# Test direct Azure OpenAI client
from shared.azure_openai import AzureOpenAIChat
client = AzureOpenAIChat(
    id="your-deployment",
    api_key="your-key",
    endpoint="your-endpoint",
    api_version="2024-02-15-preview",
    provider="azure_openai"
)
```

## Support

If you encounter issues:

1. Check the Azure OpenAI service status
2. Verify your Azure subscription has sufficient quota
3. Review the Azure OpenAI documentation
4. Check the PentestAI logs for detailed error messages

## Benefits of Azure OpenAI

- **Enterprise Security**: Enhanced security and compliance features
- **Data Privacy**: Your data stays within your Azure tenant
- **Custom Models**: Ability to fine-tune models for your specific needs
- **Integration**: Seamless integration with other Azure services
- **Reliability**: Enterprise-grade SLA and support
