#!/usr/bin/env python3
"""
PentestAI - Automated Penetration Testing System
Entry point for coordinated multi-agent penetration testing
"""

import argparse
import json
import sys
import os
import asyncio
from pathlib import Path
from typing import Dict, List, Optional
from datetime import timedelta, datetime
from dotenv import load_dotenv
from agno.models.openai import OpenAIChat
from agno.tools.mcp import MCPTools, StreamableHTTPClientParams
from agents.leadAgent import lead_agent
from shared.config import get_pentest_config, validate_config
from shared.llm_factory import LLMFactory

# Load environment variables from .env file
load_dotenv(Path(__file__).parent / '.env')


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="PentestAI - Automated Penetration Testing System",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --url https://example.com --vuln SQLI
  python main.py --url https://example.com --vuln XSS --info '{"auth_required": true, "login_path": "/login"}'
  python main.py --url https://example.com --vuln SQLI --info '["login form", "search functionality"]'
        """
    )
    
    parser.add_argument(
        "--url", 
        required=True, 
        help="Target website URL to test (e.g., https://example.com)"
    )
    
    parser.add_argument(
        "--vuln", 
        choices=["XSS", "SQLI", "ALL"], 
        required=True,
        help="Vulnerability type to target: XSS, SQLI, or ALL"
    )
    
    parser.add_argument(
        "--info", 
        type=str,
        help="Additional information about target as JSON array/object (e.g., authentication details, specific forms to test)"
    )
    
    parser.add_argument(
        "--model", 
        default="gpt-4o", 
        help="Language model to use (default: gpt-4o)"
    )
    
    parser.add_argument(
        "--output",
        help="Output file to save the vulnerability report (default: auto-generated in reports/ directory)"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose output"
    )
    
    return parser.parse_args()


def validate_url(url: str) -> str:
    """Validate and normalize the target URL."""
    if not url.startswith(('http://', 'https://')):
        url = 'https://' + url
    
    # Basic URL validation
    if not any(tld in url for tld in ['.com', '.org', '.net', '.edu', '.gov', '.io', '.co']):
        print(f"Warning: URL '{url}' may not be valid")
    
    return url


def parse_additional_info(info_str: Optional[str]) -> Optional[Dict | List]:
    """Parse additional information from JSON string."""
    if not info_str:
        return None
    
    try:
        return json.loads(info_str)
    except json.JSONDecodeError as e:
        print(f"Error parsing additional info JSON: {e}")
        print(f"Provided: {info_str}")
        sys.exit(1)


async def run_penetration_test(url: str, vulnerability_type: str, additional_info: Optional[Dict | List], model_name: str):
    """Run the penetration testing with the specified parameters."""

    print(f"🎯 Starting PentestAI Mission")
    print(f"   Target URL: {url}")
    print(f"   Vulnerability: {vulnerability_type}")
    print(f"   Model: {model_name}")
    if additional_info:
        print(f"   Additional Info: {additional_info}")
    print("-" * 60)

    # Initialize the language model
    model = OpenAIChat(id=model_name)

    # Configure MCP server connection
    server_params = StreamableHTTPClientParams(
        url="http://localhost:8931/mcp",
        timeout=timedelta(seconds=300),
        sse_read_timeout=timedelta(seconds=300)
    )
    
    print("🔧 Connecting to MCP server via streamable HTTP...")

    # Create MCP connection with proper async context management
    async with MCPTools(
        server_params=server_params,
        transport="streamable-http",
        timeout_seconds=300
    ) as mcp_tools:
        
        if vulnerability_type == "ALL":
            # Test both XSS and SQLI
            vulnerabilities = ["XSS", "SQLI"]
            all_reports = []
            
            for vuln in vulnerabilities:
                print(f"\n🔍 Testing for {vuln} vulnerabilities...")
                print("=" * 60)
                
                # Create team for this vulnerability type (within MCP context)
                team = await lead_agent(model, url, vuln, additional_info, mcp_tools)
                
                # Execute the mission
                mission_prompt = f"""
                Execute a comprehensive {vuln} vulnerability assessment on {url}.
                
                Mission Objectives:
                1. Perform systematic reconnaissance of the target website
                2. Identify potential {vuln} injection points
                3. Test identified vectors for {vuln} vulnerabilities
                4. Document any successful exploits with evidence
                5. Generate a comprehensive vulnerability report
                
                Additional Context: {additional_info or 'None provided'}
                """
                
                try:
                    report = await team.arun(mission_prompt)
                    all_reports.append({
                        "vulnerability_type": vuln,
                        "report": report
                    })
                except Exception as e:
                    print(f"❌ Error during {vuln} testing: {e}")
                    all_reports.append({
                        "vulnerability_type": vuln,
                        "error": str(e)
                    })
            
            return all_reports
        
        else:
            # Test single vulnerability type
            print(f"\n🔍 Testing for {vulnerability_type} vulnerabilities...")
            print("=" * 60)
            
            # Create the penetration testing team (within MCP context)
            team = await lead_agent(model, url, vulnerability_type, additional_info, mcp_tools)
            
            # Execute the mission
            mission_prompt = f"""
            Execute a comprehensive {vulnerability_type} vulnerability assessment on {url}.
            
            Mission Objectives:
            1. Perform systematic reconnaissance of the target website
            2. Identify potential {vulnerability_type} injection points  
            3. Test identified vectors for {vulnerability_type} vulnerabilities
            4. Document any successful exploits with evidence
            5. Generate a comprehensive vulnerability report
            
            Additional Context: {additional_info or 'None provided'}
            """
            
            try:
                report = await team.arun(mission_prompt)
                return {
                    "vulnerability_type": vulnerability_type,
                    "report": report
                }
            except Exception as e:
                print(f"❌ Error during {vulnerability_type} testing: {e}")
                return {
                    "vulnerability_type": vulnerability_type,
                    "error": str(e)
                }


def extract_technical_data(report_content: str) -> dict:
    """Extract network logs, console messages, and raw requests from report content."""
    extracted_data = {
        "network_requests": [],
        "console_logs": [],
        "raw_http_requests": [],
        "screenshots": []
    }

    try:
        # Extract network requests
        if "=== NETWORK REQUESTS ===" in report_content:
            network_section = report_content.split("=== NETWORK REQUESTS ===")[1]
            if "===" in network_section:
                network_section = network_section.split("===")[0]
            extracted_data["network_requests"] = network_section.strip()

        # Extract console messages
        if "=== CONSOLE MESSAGES ===" in report_content:
            console_section = report_content.split("=== CONSOLE MESSAGES ===")[1]
            if "===" in console_section:
                console_section = console_section.split("===")[0]
            extracted_data["console_logs"] = console_section.strip()

        # Extract raw HTTP requests
        if "=== RAW HTTP REQUEST ===" in report_content:
            import re
            raw_requests = re.findall(r'=== RAW HTTP REQUEST ===(.*?)=== END RAW REQUEST ===',
                                    report_content, re.DOTALL)
            extracted_data["raw_http_requests"] = [req.strip() for req in raw_requests]

        # Extract screenshot references
        if "screenshot" in report_content.lower():
            import re
            screenshots = re.findall(r'screenshot[_\s]*(?:taken|captured|saved)[:\s]*([^\n]+)',
                                   report_content, re.IGNORECASE)
            extracted_data["screenshots"] = screenshots

    except Exception as e:
        print(f"⚠️  Warning: Could not extract technical data: {e}")

    return extracted_data

def save_report(report, output_file: str):
    """Save the vulnerability report to file with enhanced structure."""
    try:
        # Create reports directory if it doesn't exist
        os.makedirs(os.path.dirname(output_file) if os.path.dirname(output_file) else '.', exist_ok=True)

        # Structure the report data
        if isinstance(report, list):
            # Multiple vulnerability types
            structured_report = {
                "scan_summary": {
                    "total_vulnerability_types_tested": len(report),
                    "scan_timestamp": datetime.now().isoformat(),
                    "vulnerability_types": [r.get("vulnerability_type", "Unknown") for r in report]
                },
                "findings": []
            }

            for vuln_report in report:
                if isinstance(vuln_report, dict) and "report" in vuln_report:
                    technical_data = extract_technical_data(str(vuln_report["report"]))
                    structured_finding = {
                        "vulnerability_type": vuln_report.get("vulnerability_type", "Unknown"),
                        "report_content": vuln_report["report"],
                        "technical_evidence": technical_data
                    }
                    structured_report["findings"].append(structured_finding)
                else:
                    structured_report["findings"].append(vuln_report)

        else:
            # Single vulnerability type
            technical_data = extract_technical_data(str(report.get("report", report)))
            structured_report = {
                "scan_summary": {
                    "vulnerability_type": report.get("vulnerability_type", "Unknown"),
                    "scan_timestamp": datetime.now().isoformat()
                },
                "report_content": report.get("report", report),
                "technical_evidence": technical_data
            }

        with open(output_file, 'w') as f:
            json.dump(structured_report, f, indent=2, default=str)
        print(f"\n📄 Enhanced report saved to: {output_file}")

        # Print summary of extracted data
        if isinstance(structured_report.get("technical_evidence"), dict):
            evidence = structured_report["technical_evidence"]
            print(f"   📊 Technical Evidence Extracted:")
            print(f"      - Network Requests: {'✅' if evidence.get('network_requests') else '❌'}")
            print(f"      - Console Logs: {'✅' if evidence.get('console_logs') else '❌'}")
            print(f"      - Raw HTTP Requests: {len(evidence.get('raw_http_requests', []))} found")
            print(f"      - Screenshots: {len(evidence.get('screenshots', []))} referenced")

    except Exception as e:
        print(f"❌ Error saving report: {e}")

def generate_report_filename(url: str, vulnerability_type: str) -> str:
    """Generate a timestamped report filename."""
    from datetime import datetime
    import re

    # Clean URL for filename
    clean_url = re.sub(r'https?://', '', url)
    clean_url = re.sub(r'[^\w\-_.]', '_', clean_url)

    # Generate timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create filename
    filename = f"pentest_report_{clean_url}_{vulnerability_type}_{timestamp}.json"

    return f"reports/{filename}"


async def main():
    """Main entry point for the penetration testing system."""

    # Check for required environment variables
    if not os.getenv('OPENAI_API_KEY'):
        print("❌ Error: OPENAI_API_KEY environment variable is required")
        print("Please set your OpenAI API key: export OPENAI_API_KEY='your-key-here'")
        sys.exit(1)

    # Parse command line arguments
    args = parse_arguments()

    # Validate and process inputs
    target_url = validate_url(args.url)
    additional_info = parse_additional_info(args.info)

    if args.verbose:
        print(f"🔧 Configuration:")
        print(f"   Target URL: {target_url}")
        print(f"   Vulnerability Type: {args.vuln}")
        print(f"   Model: {args.model}")
        print(f"   Additional Info: {additional_info}")
        print()

    try:
        # Run the penetration test
        result = await run_penetration_test(
            target_url,
            args.vuln,
            additional_info,
            args.model
        )

        # Save report - use specified output file or generate one automatically
        if args.output:
            output_file = args.output
        else:
            output_file = generate_report_filename(target_url, args.vuln)

        save_report(result, output_file)

        print("\n✅ Mission completed successfully!")

    except KeyboardInterrupt:
        print("\n🛑 Mission interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ Mission failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 