#!/usr/bin/env python3
"""
Example script demonstrating Azure OpenAI integration with PentestAI

This script shows how to configure and use Azure OpenAI with the PentestAI system.
"""

import os
import asyncio
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv(Path(__file__).parent / '.env')

from shared.config import get_pentest_config, validate_config
from shared.llm_factory import LLMFactory
from shared.azure_openai import AzureOpenAIChat

async def test_azure_openai_configuration():
    """Test Azure OpenAI configuration and basic functionality"""
    
    print("🚀 Testing Azure OpenAI Configuration")
    print("=" * 50)
    
    try:
        # Load configuration
        config = get_pentest_config()
        print(f"✅ Configuration loaded")
        print(f"   Provider: {config.llm.provider}")
        print(f"   Model/Deployment: {config.llm.model}")
        
        if config.llm.provider == "azure_openai":
            print(f"   Endpoint: {config.llm.endpoint}")
            print(f"   API Version: {config.llm.api_version}")
        
        # Validate configuration
        validate_config(config)
        print("✅ Configuration validation passed")
        
        # Create LLM instance
        llm = LLMFactory.create_llm(config.llm)
        print("✅ LLM instance created successfully")
        
        # Test basic chat functionality
        if hasattr(llm, 'chat'):
            print("\n🧪 Testing basic chat functionality...")
            test_messages = [
                {"role": "user", "content": "Hello! Can you confirm you're working correctly? Please respond with 'Azure OpenAI is working!'"}
            ]
            
            response = llm.chat(test_messages)
            print(f"✅ Chat response: {response}")
        
        print("\n🎉 Azure OpenAI integration test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Check your .env file has the correct Azure OpenAI configuration:")
        print("   - LLM_PROVIDER=azure_openai")
        print("   - AZURE_OPENAI_API_KEY=your_api_key")
        print("   - AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/")
        print("   - AZURE_OPENAI_DEPLOYMENT=your_deployment_name")
        print("   - AZURE_OPENAI_API_VERSION=2024-02-15-preview")
        print("2. Verify your Azure OpenAI resource is properly configured")
        print("3. Check that your deployment name matches the model you want to use")

def print_configuration_example():
    """Print example configuration for Azure OpenAI"""
    
    print("\n📋 Example .env Configuration for Azure OpenAI")
    print("=" * 50)
    print("""
# LLM Provider Configuration
LLM_PROVIDER=azure_openai

# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=gpt-4o  # Your deployment name
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Other configuration...
MCP_SERVER_URL=http://localhost:8931/mcp
MCP_SERVER_PORT=8931
BROWSER_TYPE=chromium
BROWSER_HEADLESS=true
REPORTS_DIR=./reports
""")

async def main():
    """Main function"""
    print_configuration_example()
    await test_azure_openai_configuration()

if __name__ == "__main__":
    asyncio.run(main())
