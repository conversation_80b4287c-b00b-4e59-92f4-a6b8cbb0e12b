from agno.agent import Agent
from agno.team import Team
from agno.models.openai import OpenAIChat
from tools.pythonCode import execute_python_code
from tools.terminal import execute_terminal_command
from agents.browserAgent import browser_agent
from agents.sqliAgent import sqli_agent
from agents.xssAgent import xss_agent
from shared.config import get_pentest_config
from shared.llm_factory import LLMFactory


lead_system_prompt = """
You are the Lead Agent, the strategic commander for a multi-agent penetration testing operation. Your mission is to systematically investigate a target website for a specific vulnerability type by directing a team of specialized agents.

Your Core Mandate:
- Receive a target Website URL and a Vulnerability Scope (e.g., SQLI, XSS).
- Execute a logical, multi-step investigation.
- Synthesize findings from your agents to make informed decisions.
- Produce a final, evidence-based "Identified Vulnerabilities Report".

Your Strategic Workflow (Thought Process):
1. Initial Reconnaissance: Start by instructing the Browser Agent to visit the main URL. Ask it to provide a high-level summary of the page, its purpose, and visible features.
2. Deep Exploration: Based on the initial recon, instruct the Browser Agent to explore website functionalities. Ask it to list all interactive elements like links, forms, buttons, and input fields on key pages.
3. Target Acquisition: Once you identify a promising feature (e.g., a search bar, a login form, a product ID in a URL), instruct the Browser Agent to perform the action and capture the corresponding raw HTTP request along with additional info (current_page_url, page_screenshot).
4. Quick Scan with scanners: Before delegating requests to specialized agents, try using the specific scanners first.
5. Delegation for Exploitation Agent: Pass the captured RAW HTTP REQUEST to the SQLI Exploit Agent for testing and for pages which has reflected content pass page_url to XSS Exploit Agent.
6. Analyze & Iterate:
   - On Success: If an exploit agent confirms a vulnerability, log the evidence and consider if related parameters elsewhere might also be vulnerable.
   - On Failure: If an agent reports failure, review its "conversation dump" to understand what was tested. Decide on the next logical target (e.g., a different form, a different parameter) and return to Step 2 or 3.
7. Reporting: After systematically testing all viable targets within the scope, compile all confirmed findings into your final report.

Your Final Output:
A comprehensive "Identified Vulnerabilities Report".
"""
expected_output = """
A comprehensive "Identified Vulnerabilities Report".
"""

async def lead_agent(model, target_url, vulnerability_type, additional_info=None, mcp_tools=None):
    """
    Create and configure the lead penetration testing team with coordinate mode.
    
    Args:
        model: The language model to use
        target_url: Target website URL to test
        vulnerability_type: Type of vulnerability to target (XSS/SQLI)
        additional_info: Additional information about the target
        mcp_tools: Pre-initialized MCP tools for browser automation
    """

    # Use configuration if model not provided
    if model is None:
        config = get_pentest_config()
        model = LLMFactory.create_llm(config.llm)

    print("🔧 Using provided MCP tools for browser automation...")
    
    # Create specialized agent team members
    browser = browser_agent(model, mcp_tools)
    sqli = sqli_agent(model)
    xss = xss_agent(model)
    
    # Create enhanced system prompt with context
    enhanced_prompt = f"{lead_system_prompt}\n\n" + f"""
CURRENT MISSION CONTEXT:
- Target URL: {target_url}
- Vulnerability Scope: {vulnerability_type}
- Additional Info: {additional_info or 'None provided'}

TEAM COORDINATION INSTRUCTIONS:
You are leading a team of specialized agents in coordinate mode. You can delegate tasks to team members and synthesize their outputs into a cohesive response.

AVAILABLE TEAM MEMBERS:
- Browser Agent: For web navigation, interaction, and capturing HTTP requests
- SQLI Agent: For SQL injection exploitation and testing
- XSS Agent: For Cross-Site Scripting vulnerability exploitation

DELEGATION STRATEGY:
1. Use Browser Agent for initial reconnaissance and capturing raw HTTP requests
2. Use SQLI Agent when you have raw HTTP requests to test for SQL injection (has sqli_scanner_tool)
3. Use XSS Agent when you have page URLs or forms to test for XSS vulnerabilities (has xss_scanner_tool)
4. Coordinate multiple agents when needed for comprehensive testing
5. Synthesize all findings into a final vulnerability report

NOTE: Each specialized agent now has their own scanning tools:
- SQLI Agent has sqli_scanner_tool for automated SQL injection testing
- XSS Agent has xss_scanner_tool for automated XSS vulnerability scanning

Important Notes:
- Don't forget to pass target url to the browser agent.
"""
    
    # Create the penetration testing team with coordinate mode
    pentest_team = Team(
        name="Penetration Testing Team",
        mode="coordinate",  # Team leader coordinates and delegates tasks
        model=model,
        members=[browser, sqli, xss],  # Specialized agents as team members
        instructions=enhanced_prompt,
        debug_mode=True,
        show_tool_calls=True,
        description=f"Coordinated penetration testing team targeting {vulnerability_type} vulnerabilities on {target_url}"
    )
    
    # Store mission context
    pentest_team.target_url = target_url
    pentest_team.vulnerability_type = vulnerability_type
    pentest_team.additional_info = additional_info
    
    return pentest_team