# PentestAI Configuration
# Copy this file to .env and configure with your actual values

# =============================================================================
# LLM Provider Configuration
# =============================================================================

# Choose your LLM provider: openai, azure_openai, gemini
LLM_PROVIDER=azure_openai

# OpenAI Configuration (if using LLM_PROVIDER=openai)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o

# Azure OpenAI Configuration (if using LLM_PROVIDER=azure_openai)
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource-name.openai.azure.com/
AZURE_OPENAI_DEPLOYMENT=your_deployment_name_here
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Gemini Configuration (if using LLM_PROVIDER=gemini)
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro

# =============================================================================
# MCP Server Configuration
# =============================================================================

MCP_SERVER_URL=http://localhost:8931/mcp
MCP_SERVER_PORT=8931
MCP_TIMEOUT=300

# =============================================================================
# Browser Configuration
# =============================================================================

BROWSER_TYPE=chromium
BROWSER_HEADLESS=true
BROWSER_TIMEOUT=30000

# =============================================================================
# Tool Configuration
# =============================================================================

# SQLMap Configuration
SQLMAP_PATH=sqlmap
SQLMAP_TIMEOUT=300

# Scan Configuration
SCAN_TIMEOUT=600
AGENT_TIMEOUT=300

# =============================================================================
# Output Configuration
# =============================================================================

REPORTS_DIR=./reports
LOG_LEVEL=INFO

# =============================================================================
# Rate Limiting Configuration
# =============================================================================

MAX_TOKENS_PER_REQUEST=25000
RECONNAISSANCE_PHASES=3
PHASE_DELAY_SECONDS=2
MAX_RETRIES=3
