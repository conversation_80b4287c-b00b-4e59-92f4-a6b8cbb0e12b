"""
LLM Factory for creating different LLM providers

Provides a unified interface for creating LLM instances
with support for OpenAI, Azure OpenAI, and Gemini (future).
"""

from typing import Any
from agno.models.openai import OpenAIChat
from agno.models.azure import Azure<PERSON>penAI
from .azure_openai import <PERSON>zure<PERSON>pen<PERSON><PERSON>hat
from .config import LLMConfig

class LLMFactory:
    """Factory class for creating LLM instances"""

    @staticmethod
    def create_llm(config: LLMConfig) -> Any:
        """Create LLM instance based on configuration"""
        if config.provider == "openai":
            return OpenAIChat(
                id=config.model,
                api_key=config.api_key
            )
        elif config.provider == "azure_openai":
            return AzureOpenAIChat(
                id=config.model,
                api_key=config.api_key,
                endpoint=config.endpoint,
                api_version=config.api_version,
                provider=config.provider
            )
        elif config.provider == "gemini":
            # TODO: Implement Gemini support when needed
            # from agno.models.gemini import GeminiChat
            # return GeminiChat(
            #     id=config.model,
            #     api_key=config.api_key
            # )
            raise NotImplementedError("Gemini support not yet implemented")
        else:
            raise ValueError(f"Unsupported LLM provider: {config.provider}")

    @staticmethod
    def get_supported_providers() -> list:
        """Get list of supported LLM providers"""
        return ["openai", "azure_openai"]  # Add "gemini" when implemented
